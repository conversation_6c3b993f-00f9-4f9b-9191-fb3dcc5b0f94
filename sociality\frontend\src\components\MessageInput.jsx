import {
	Flex,
	Input,
	InputGroup,
	InputRightElement,
	InputLeftElement,
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	Spinner,
	useDisclosure,
	Tooltip,
	Box,
	Text,
	Button,
	Icon,
	IconButton,
	Divider,
	Progress,
	Popover,
	PopoverTrigger,
	PopoverContent,
	PopoverBody,
	Portal,
	useColorModeValue,
	Textarea,
	HStack,
	VStack,
} from "@chakra-ui/react";
import { IoSendSharp, IoAttach, IoMicOutline, IoHappyOutline, IoImageOutline } from "react-icons/io5";
import { FaPaperclip, FaSmile, FaMicrophone, FaFileAlt } from "react-icons/fa";
import { MdEmojiEmotions, MdAttachFile } from "react-icons/md";
import { CloseIcon } from "@chakra-ui/icons";
import useShowToast from "../hooks/useShowToast";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { user<PERSON><PERSON>, conversations<PERSON><PERSON>, selected<PERSON>onversation<PERSON><PERSON> } from "../atoms";
import usePreviewImg from "../hooks/usePreviewImg";
import { useSocket } from "../hooks/useSocket";
import { memo, useRef, useState, useCallback, useEffect } from "react";
import { fetchWithSession } from "../utils/api";
import "../styles/telegram-input.css";
import "../styles/emoji-picker.css";
import EmojiPicker from 'emoji-picker-react';
import { useRecentEmojis } from "../hooks/useRecentEmojis";
import SimpleEmojiPicker from "./SimpleEmojiPicker";


const MessageInput = memo(({ setMessages }) => {
	const [messageText, setMessageText] = useState("");
	const [isRecording, setIsRecording] = useState(false);
	const { isOpen: showAttachMenu, onOpen: openAttachMenu, onClose: closeAttachMenu } = useDisclosure();
	const [textareaHeight, setTextareaHeight] = useState(40);
	const textareaRef = useRef(null);
	const fileInputRef = useRef(null);
	const showToast = useShowToast();
	const selectedConversation = useRecoilValue(selectedConversationAtom);
	const setConversations = useSetRecoilState(conversationsAtom);
	const currentUser = useRecoilValue(userAtom);
	const { socket, isConnected } = useSocket();
	const { isOpen: isImageOpen, onOpen: onImageOpen, onClose: onImageClose } = useDisclosure();
	const { isOpen: isEmojiOpen, onOpen: onEmojiOpen, onClose: onEmojiClose } = useDisclosure();


	const { handleImageChange, imgUrl, setImgUrl } = usePreviewImg();
	const [isSending, setIsSending] = useState(false);
	const [selectedEmoji, setSelectedEmoji] = useState("");
	const { recentEmojis, addRecentEmoji } = useRecentEmojis();
	const [useSimplePicker, setUseSimplePicker] = useState(true); // Use simple picker by default
	const emojiPickerRef = useRef(null);

	// File upload state
	const [selectedFile, setSelectedFile] = useState(null);
	const [filePreview, setFilePreview] = useState(null);
	const [uploadProgress, setUploadProgress] = useState(0);
	const attachMenuRef = useRef(null);

	// Theme-aware colors
	const inputBgColor = useColorModeValue("#f8f9fa", "#2d2d2d");
	const inputBorderColor = useColorModeValue("#e0e0e0", "#404040");
	const textColor = useColorModeValue("gray.800", "white");
	const placeholderColor = useColorModeValue("gray.500", "gray.400");
	const buttonHoverBg = useColorModeValue("gray.100", "whiteAlpha.100");
	const containerBg = useColorModeValue("white", "#1a1a1a");
	const borderTopColor = useColorModeValue("gray.200", "gray.700");
	const emojiPickerTextColor = useColorModeValue("gray.800", "whiteAlpha.900");
	const menuButtonColor = useColorModeValue("gray.600", "whiteAlpha.800");
	const menuButtonHoverBg = useColorModeValue("rgba(59, 130, 246, 0.1)", "rgba(59, 130, 246, 0.1)");
	const menuButtonHoverColor = useColorModeValue("#3b82f6", "#3b82f6");

	// Quick emoji reactions (frequently used)
	const quickEmojis = ["👍", "❤️", "😂", "😮", "😢", "😡", "🔥", "👏"];

	// Auto-resize textarea
	useEffect(() => {
		if (textareaRef.current) {
			textareaRef.current.style.height = 'auto';
			const scrollHeight = textareaRef.current.scrollHeight;
			const newHeight = Math.min(Math.max(scrollHeight, 40), 120); // Min 40px, max 120px
			setTextareaHeight(newHeight);
			textareaRef.current.style.height = `${newHeight}px`;
		}
	}, [messageText]);

	// Click outside handler and keyboard support for emoji picker
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target) && isEmojiOpen) {
				onEmojiClose();
			}
		};

		const handleKeyDown = (event) => {
			if (event.key === 'Escape' && isEmojiOpen) {
				onEmojiClose();
			}
		};

		if (isEmojiOpen) {
			document.addEventListener('mousedown', handleClickOutside);
			document.addEventListener('touchstart', handleClickOutside);
			document.addEventListener('keydown', handleKeyDown);
		}

		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
			document.removeEventListener('touchstart', handleClickOutside);
			document.removeEventListener('keydown', handleKeyDown);
		};
	}, [isEmojiOpen, onEmojiClose]);



	// Handle file attachment
	const handleFileAttachment = useCallback(() => {
		console.log("File attachment clicked");
		closeAttachMenu(); // Close the menu
		const input = document.createElement('input');
		input.type = 'file';
		input.accept = '*/*';
		input.onchange = (e) => {
			console.log("File selected:", e.target.files[0]);
			handleFileChange(e);
		};
		input.click();
	}, [closeAttachMenu]);

	// Handle image attachment
	const handleImageAttachment = useCallback(() => {
		console.log("Image attachment clicked");
		closeAttachMenu(); // Close the menu
		const input = document.createElement('input');
		input.type = 'file';
		input.accept = 'image/*';
		input.onchange = (e) => {
			console.log("Image selected:", e.target.files[0]);
			handleImageChange(e);
		};
		input.click();
	}, [handleImageChange, closeAttachMenu]);

	// Handle file change
	const handleFileChange = useCallback((e) => {
		const file = e.target.files[0];
		if (!file) return;

		// Check file size (50MB limit)
		const maxSize = 50 * 1024 * 1024; // 50MB
		if (file.size > maxSize) {
			showToast("Error", "File size must be less than 50MB", "error");
			return;
		}

		setSelectedFile(file);

		// Create preview for different file types
		if (file.type.startsWith('image/')) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setFilePreview({
					type: 'image',
					url: e.target.result,
					name: file.name,
					size: file.size
				});
			};
			reader.readAsDataURL(file);
		} else {
			setFilePreview({
				type: 'file',
				name: file.name,
				size: file.size,
				extension: file.name.split('.').pop()?.toUpperCase() || 'FILE'
			});
		}
	}, [showToast]);

	// Clear file selection
	const clearFileSelection = useCallback(() => {
		setSelectedFile(null);
		setFilePreview(null);
		setUploadProgress(0);
	}, []);

	// Handle voice recording (placeholder)
	const handleVoiceRecording = useCallback(() => {
		setIsRecording(!isRecording);
		// Add actual voice recording logic here
		showToast("Info", "Voice recording feature coming soon!", "info");
	}, [isRecording, showToast]);



	// Define sendRequestFn to handle both regular and federated messages
	const sendRequestFn = async (formData, messageData) => {
		let responseData = null;

		// Handle federated messages
		if (selectedConversation.isFederated) {
			console.log("Sending federated message:", messageData.text);
			const response = await fetchWithSession("/api/cross-platform/rooms/" + selectedConversation._id + "/messages", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					message: messageData.text
				}),
			});
			responseData = await response.json();
			console.log("Federated message response:", responseData);

			// For federated messages, transform the response to match expected format
			if (responseData.success && responseData.localMessage) {
				return {
					_id: responseData.localMessage.id,
					text: responseData.localMessage.text,
					sender: responseData.localMessage.sender._id,
					senderUsername: responseData.localMessage.sender.username,
					senderPlatform: responseData.localMessage.sender.platform,
					createdAt: responseData.localMessage.timestamp,
					isFederated: true,
					platform: responseData.localMessage.platform,
					tempId: messageData.tempId
				};
			}
		} else {
			// Handle regular messages
			if (formData) {
				const response = await fetchWithSession("/api/messages", {
					method: "POST",
					body: formData,
				});
				responseData = await response.json();
			} else {
				const response = await fetchWithSession("/api/messages", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(messageData),
				});
				responseData = await response.json();
			}
		}

		console.log("Message sent successfully (server response):", responseData);
		return responseData;
	};

	// PATCH: Define messageAlreadyUpdated utility
	const messageAlreadyUpdated = (prev, tempId, responseData) => {
		return prev.some(msg =>
			(msg.tempId === tempId && !msg.isOptimistic) ||
			(responseData._id && msg._id === responseData._id)
		);
	};

	// Handle message submission
	const handleSendMessage = useCallback(async (e) => {
		e?.preventDefault();
		if (isSending) return;
		if (!messageText.trim() && !imgUrl && !selectedEmoji && !selectedFile) {
			console.warn('Attempted to send empty message. Aborting.');
			return;
		}
		setIsSending(true);
		try {
			const tempId = Date.now().toString();
			let formData = null;
			let messageData = {
				tempId,
				text: messageText,
				recipientId: selectedConversation.userId,
				img: imgUrl || undefined,
				emoji: selectedEmoji || undefined,
			};

			// For federated messages, don't use images/emojis/files for now
			if (selectedConversation.isFederated && (imgUrl || selectedEmoji || selectedFile)) {
				showToast("Info", "Images, files and emojis are not supported in cross-platform rooms yet", "info");
				setIsSending(false);
				return;
			}

			// Prepare message with any media type
			if (imgUrl || selectedFile) {
				formData = new FormData();
				formData.append("text", messageText);
				formData.append("recipientId", selectedConversation.userId);
				formData.append("tempId", tempId);

				console.log("Preparing FormData with:", {
					text: messageText,
					hasImg: !!imgUrl,
					hasFile: !!selectedFile,
					hasEmoji: !!selectedEmoji
				});

				if (imgUrl) {
					if (imgUrl.startsWith("blob:")) {
						// Handle blob URLs
						const response = await fetch(imgUrl);
						const imgBlob = await response.blob();
						formData.append("img", imgBlob);
						console.log("Added image blob to FormData, size:", imgBlob.size);
					} else if (imgUrl.startsWith("data:")) {
						// Handle data URLs (base64)
						const response = await fetch(imgUrl);
						const imgBlob = await response.blob();
						formData.append("img", imgBlob);
						console.log("Added image data URL to FormData, size:", imgBlob.size);
					} else {
						// Handle regular URLs
						formData.append("img", imgUrl);
						console.log("Added image URL to FormData:", imgUrl);
					}
				}

				if (selectedFile) {
					formData.append("file", selectedFile);
					formData.append("fileName", selectedFile.name);
					formData.append("fileSize", selectedFile.size);
					console.log("Added file to FormData:", selectedFile.name, selectedFile.size);
				}

				if (selectedEmoji) {
					formData.append("emoji", selectedEmoji);
					console.log("Added emoji to FormData:", selectedEmoji);
				}
			}
			// Create display text for message
			const displayText = messageText ||
				(selectedFile ? `📎 ${selectedFile.name}` : '') ||
				(imgUrl ? '🖼️ Image' : '') ||
				(selectedEmoji ? selectedEmoji : '');

			const optimisticMessage = selectedConversation.isFederated ? {
				_id: tempId,
				text: displayText,
				sender: currentUser._id,
				senderUsername: currentUser.name || currentUser.username,
				senderPlatform: 'sociality',
				createdAt: new Date().toISOString(),
				isOptimistic: true,
				isNew: true,
				isFederated: true,
				platform: 'sociality',
				tempId
			} : {
				text: displayText,
				sender: currentUser._id,
				tempId,
				createdAt: new Date().toISOString(),
				isOptimistic: true,
				isNew: true,
				img: imgUrl || undefined,
				emoji: selectedEmoji || undefined,
				file: selectedFile ? URL.createObjectURL(selectedFile) : undefined,
				fileName: selectedFile?.name || undefined,
				fileSize: selectedFile?.size || undefined,
			};
			setMessages(prev => [...prev, optimisticMessage]);

			// Immediately update conversations to keep user in recent contacts
			setConversations(prev => {
				const updatedConversations = [...prev];
				const conversationIndex = updatedConversations.findIndex(c => c._id === selectedConversation._id);
				if (conversationIndex !== -1) {
					// Create last message text for conversation list
					const lastMessageText = messageText ||
						(selectedFile ? `📎 ${selectedFile.name}` : '') ||
						(imgUrl ? '🖼️ Image' : '') ||
						(selectedEmoji ? selectedEmoji : '');

					updatedConversations[conversationIndex] = {
						...updatedConversations[conversationIndex],
						lastMessage: {
							text: lastMessageText,
							sender: currentUser._id,
							img: imgUrl ? true : undefined,
							emoji: selectedEmoji || undefined,
							file: selectedFile ? true : undefined,
							fileName: selectedFile?.name || undefined,
							createdAt: new Date().toISOString(),
						},
						updatedAt: new Date().toISOString(),
					};
					// Move conversation to top
					const conversation = updatedConversations.splice(conversationIndex, 1)[0];
					updatedConversations.unshift(conversation);
				}
				return updatedConversations;
			});

			// Immediate aggressive scroll trigger for optimistic message
			const forceImmediateScroll = () => {
				const messageContainer = document.getElementById('messageListContainer');
				if (messageContainer) {
					console.log('📤 Immediate aggressive scroll after sending message');
					// Force scroll to absolute maximum
					messageContainer.scrollTop = messageContainer.scrollHeight;
					console.log('📤 Set scrollTop to:', messageContainer.scrollTop);
				}
			};

			// Multiple immediate scroll attempts
			setTimeout(forceImmediateScroll, 10);
			setTimeout(forceImmediateScroll, 50);
			setTimeout(forceImmediateScroll, 100);

			try {
				console.log("Waiting for server to process message...");
				const responseData = await sendRequestFn(formData, messageData);
				setMessages(prev => {
					if (messageAlreadyUpdated(prev, tempId, responseData)) {
						console.log("Message already updated by socket, skipping update");
						return prev;
					}
					const updatedMessages = prev.map(msg =>
						msg.tempId === tempId ? { ...responseData, isNew: true } : msg
					);

					return updatedMessages;
				});
				setConversations(prev => {
					const updatedConversations = [...prev];
					const conversationIndex = updatedConversations.findIndex(c => c._id === selectedConversation._id);
					if (conversationIndex !== -1) {
						// Create last message text for conversation list
						const lastMessageText = messageText ||
							(selectedFile ? `📎 ${selectedFile.name}` : '') ||
							(imgUrl ? '🖼️ Image' : '') ||
							(selectedEmoji ? selectedEmoji : '');

						updatedConversations[conversationIndex] = {
							...updatedConversations[conversationIndex],
							lastMessage: {
								text: lastMessageText,
								sender: currentUser._id,
								img: imgUrl ? true : undefined,
								emoji: selectedEmoji || undefined,
								file: selectedFile ? true : undefined,
								fileName: selectedFile?.name || undefined,
								createdAt: new Date().toISOString(),
							},
							updatedAt: new Date().toISOString(),
						};
						// Move conversation to top
						const conversation = updatedConversations.splice(conversationIndex, 1)[0];
						updatedConversations.unshift(conversation);
					}
					return updatedConversations;
				});


			} catch (error) {
				showToast("Error", error.message, "error");
			}
			document.getElementById('messageInput')?.blur();
			setMessageText("");
			setImgUrl("");
			setSelectedEmoji("");
			clearFileSelection();
		} catch (error) {
			showToast("Error", error.message, "error");
		} finally {
			setIsSending(false);
		}
	}, [
		messageText,
		imgUrl,
		selectedEmoji,
		selectedFile,
		selectedConversation?.userId,
		selectedConversation?.isFederated,
		selectedConversation?._id,
		currentUser?._id,
		setMessages,
		showToast,
		setImgUrl,
		setConversations,
		isSending,
		clearFileSelection
	]);

	// Update emoji click handler with proper handleSendMessage reference
	const handleEmojiClickWithSend = useCallback((emojiData, event) => {
		const emoji = emojiData.emoji;

		// Add to recent emojis
		addRecentEmoji(emoji);

		// Check if Ctrl/Cmd key is pressed for standalone emoji message
		if (event?.ctrlKey || event?.metaKey) {
			// Send as standalone emoji message
			setSelectedEmoji(emoji);
			onEmojiClose();
			// Auto-send emoji message
			setTimeout(() => {
				const syntheticEvent = { preventDefault: () => {} };
				handleSendMessage(syntheticEvent);
			}, 100);
		} else {
			// Add to text input
			setMessageText(prev => prev + emoji);
			// Keep picker open for multiple emoji selection
		}
	}, [addRecentEmoji, onEmojiClose, handleSendMessage]);

	// Update quick emoji send handler
	const handleQuickEmojiSendWithRef = useCallback((emoji) => {
		addRecentEmoji(emoji);
		setSelectedEmoji(emoji);
		onEmojiClose();
		// Auto-send emoji message
		setTimeout(() => {
			const syntheticEvent = { preventDefault: () => {} };
			handleSendMessage(syntheticEvent);
		}, 100);
	}, [addRecentEmoji, onEmojiClose, handleSendMessage]);

	// Close attachment menu when clicking outside
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (attachMenuRef.current && !attachMenuRef.current.contains(event.target)) {
				closeAttachMenu();
			}
		};

		if (showAttachMenu) {
			document.addEventListener('mousedown', handleClickOutside);
		}

		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [showAttachMenu, closeAttachMenu]);

	// Handle Enter key press
	const handleKeyDown = useCallback((e) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			console.log('Enter key pressed, message text:', messageText);
			handleSendMessage(e);
		}
	}, [handleSendMessage, messageText]);

	return (
		<Box
			bg={containerBg}
			p={2} // Reduced padding to minimize extra space
		>
			{/* Image preview */}
			{imgUrl && (
				<Box mb={3} className="telegram-image-preview">
					<HStack spacing={3}>
						<Box
							w="60px"
							h="60px"
							borderRadius="md"
							overflow="hidden"
							bg={useColorModeValue("gray.200", "gray.100")}
							backgroundImage={`url(${imgUrl})`}
							backgroundSize="cover"
							backgroundPosition="center"
						/>
						<VStack align="start" spacing={1} flex={1}>
							<Text fontSize="sm" fontWeight="medium" color={textColor}>
								Image Preview
							</Text>
							<Text fontSize="xs" color={useColorModeValue("gray.600", "gray.500")}>
								Image ready to send
							</Text>
						</VStack>
						<IconButton
							icon={<CloseIcon />}
							size="sm"
							variant="ghost"
							onClick={() => setImgUrl("")}
							aria-label="Remove image"
						/>
					</HStack>
				</Box>
			)}

			{/* File preview */}
			{filePreview && (
				<Box mb={3} className="telegram-file-preview">
					<HStack spacing={3} p={3} bg={useColorModeValue("gray.50", "gray.800")} borderRadius="md" border="1px" borderColor={useColorModeValue("gray.200", "gray.600")}>
						<Box
							w="50px"
							h="50px"
							borderRadius="md"
							bg={useColorModeValue("blue.100", "blue.900")}
							display="flex"
							alignItems="center"
							justifyContent="center"
						>
							{filePreview.type === 'image' ? (
								<Box
									w="50px"
									h="50px"
									borderRadius="md"
									overflow="hidden"
									backgroundImage={`url(${filePreview.url})`}
									backgroundSize="cover"
									backgroundPosition="center"
								/>
							) : (
								<FaFileAlt size={20} color={useColorModeValue("#3182ce", "#63b3ed")} />
							)}
						</Box>
						<VStack align="start" spacing={1} flex={1}>
							<Text fontSize="sm" fontWeight="medium" color={textColor} noOfLines={1}>
								{filePreview.name}
							</Text>
							<Text fontSize="xs" color={useColorModeValue("gray.600", "gray.500")}>
								{filePreview.type === 'image' ? 'Image' : filePreview.extension} • {(filePreview.size / 1024).toFixed(1)} KB
							</Text>
						</VStack>
						<IconButton
							icon={<CloseIcon />}
							size="sm"
							variant="ghost"
							onClick={clearFileSelection}
							aria-label="Remove file"
						/>
					</HStack>
				</Box>
			)}

			<Flex gap={2} alignItems="flex-end">
				{/* Attachment button with custom menu */}
				<Box position="relative" ref={attachMenuRef}>
					<IconButton
						icon={<MdAttachFile />}
						size="lg"
						variant="ghost"
						colorScheme="gray"
						borderRadius="full"
						onClick={(e) => {
							console.log("Attachment button clicked", showAttachMenu);
							e.preventDefault();
							e.stopPropagation();
							if (showAttachMenu) {
								closeAttachMenu();
							} else {
								openAttachMenu();
							}
						}}
						className="telegram-attach-button"
						aria-label="Attach file"
						h="44px"
						w="44px"
					/>

					{/* Custom dropdown menu */}
					{showAttachMenu && (
						<Box
							position="absolute"
							bottom="50px"
							left="0"
							bg={useColorModeValue("white", "gray.800")}
							border="1px solid"
							borderColor={useColorModeValue("gray.200", "gray.600")}
							borderRadius="md"
							boxShadow="lg"
							p={2}
							w="160px"
							zIndex={1000}
							className="telegram-attachment-menu"
						>
							<VStack spacing={1}>
								<Button
									leftIcon={<IoImageOutline />}
									variant="ghost"
									size="sm"
									w="full"
									justifyContent="flex-start"
									onClick={(e) => {
										console.log("Image button clicked");
										e.preventDefault();
										e.stopPropagation();
										handleImageAttachment();
									}}
									_hover={{ bg: useColorModeValue("gray.100", "gray.700") }}
									color={useColorModeValue("gray.700", "gray.200")}
								>
									📷 Photo
								</Button>
								<Button
									leftIcon={<FaPaperclip />}
									variant="ghost"
									size="sm"
									w="full"
									justifyContent="flex-start"
									onClick={(e) => {
										console.log("File button clicked");
										e.preventDefault();
										e.stopPropagation();
										handleFileAttachment();
									}}
									_hover={{ bg: useColorModeValue("gray.100", "gray.700") }}
									color={useColorModeValue("gray.700", "gray.200")}
								>
									📎 File
								</Button>
							</VStack>
						</Box>
					)}
				</Box>

				{/* Main input area */}
				<Box
					flex={1}
					className="telegram-input-container"
					borderRadius="24px"
					p={2}
				>
					<HStack spacing={2} align="flex-end">
						{/* Emoji button */}
						<Box position="relative">
							<IconButton
								icon={<MdEmojiEmotions />}
								size="sm"
								variant="ghost"
								colorScheme="gray"
								borderRadius="full"
								onClick={(e) => {
									e.preventDefault();
									e.stopPropagation();
									onEmojiOpen();
								}}
								className="telegram-emoji-button"
								aria-label="Add emoji"
								_hover={{
									bg: useColorModeValue("gray.100", "whiteAlpha.100"),
									color: "#00CC85"
								}}
							/>

							{/* Telegram-style Emoji Picker Popup */}
							{isEmojiOpen && (
								<Box
									ref={emojiPickerRef}
									position="absolute"
									bottom="45px"
									right="-200px"
									w="280px"
									h="320px"
									bg={useColorModeValue("rgba(255, 255, 255, 0.95)", "rgba(45, 45, 45, 0.95)")}
									backdropFilter="blur(10px)"
									border="1px solid"
									borderColor={useColorModeValue("gray.200", "gray.600")}
									borderRadius="xl"
									boxShadow="0 20px 40px rgba(0, 0, 0, 0.15)"
									zIndex={1000}
									className="telegram-emoji-picker"
									overflow="hidden"
									_after={{
										content: '""',
										position: "absolute",
										bottom: "-8px",
										left: "220px",
										width: 0,
										height: 0,
										borderLeft: "8px solid transparent",
										borderRight: "8px solid transparent",
										borderTop: `8px solid ${useColorModeValue("rgba(255, 255, 255, 0.95)", "rgba(45, 45, 45, 0.95)")}`,
									}}
								>
										{/* Close button */}
										<Box
											position="absolute"
											top="8px"
											right="8px"
											zIndex={1001}
										>
											<IconButton
												icon={<CloseIcon />}
												size="sm"
												variant="ghost"
												onClick={onEmojiClose}
												aria-label="Close emoji picker"
												_hover={{ bg: useColorModeValue("gray.200", "rgba(255, 255, 255, 0.1)") }}
											/>
										</Box>

										{/* Emoji picker content */}
										{useSimplePicker ? (
											<SimpleEmojiPicker
												onEmojiClick={handleEmojiClickWithSend}
												onClose={onEmojiClose}
											/>
										) : (
											<>
												<Box
											bg={useColorModeValue("gray.50", "#1a1a1a")}
											px={3}
											py={2}
											borderBottom="1px solid"
											borderColor={useColorModeValue("gray.200", "gray.600")}
											position="relative"
										>
											<Flex justify="space-between" align="center">
												<Box>
													<Text fontSize="sm" fontWeight="medium" color={emojiPickerTextColor}>
														Choose an emoji
													</Text>
													<Text fontSize="xs" color={useColorModeValue("gray.500", "gray.400")} mt={1}>
														Click to add • Ctrl+Click to send
													</Text>
												</Box>
												<IconButton
													icon={<CloseIcon />}
													size="sm"
													variant="ghost"
													onClick={onEmojiClose}
													aria-label="Close emoji picker"
													_hover={{ bg: useColorModeValue("gray.200", "rgba(255, 255, 255, 0.1)") }}
												/>
											</Flex>

											{/* Recent emojis */}
											{recentEmojis.length > 0 && (
												<>
													<Text fontSize="xs" fontWeight="medium" color={emojiPickerTextColor} mt={2} mb={1}>
														Recently used
													</Text>
													<Flex gap={1} flexWrap="wrap" mb={2}>
														{recentEmojis.slice(0, 8).map((emoji, index) => (
															<Button
																key={index}
																variant="ghost"
																size="sm"
																fontSize="lg"
																onClick={() => handleQuickEmojiSendWithRef(emoji)}
																_hover={{
																	bg: useColorModeValue("gray.200", "rgba(255, 255, 255, 0.1)"),
																	transform: "scale(1.1)"
																}}
																borderRadius="md"
																p={1}
																minW="auto"
																h="auto"
																transition="all 0.2s"
															>
																{emoji}
															</Button>
														))}
													</Flex>
												</>
											)}

											{/* Quick emoji reactions */}
											<Text fontSize="xs" fontWeight="medium" color={emojiPickerTextColor} mb={1}>
												Quick reactions
											</Text>
											<Flex gap={1} flexWrap="wrap">
												{quickEmojis.map((emoji, index) => (
													<Button
														key={index}
														variant="ghost"
														size="sm"
														fontSize="lg"
														onClick={() => handleQuickEmojiSendWithRef(emoji)}
														_hover={{
															bg: useColorModeValue("gray.200", "rgba(255, 255, 255, 0.1)"),
															transform: "scale(1.1)"
														}}
														borderRadius="md"
														p={1}
														minW="auto"
														h="auto"
														transition="all 0.2s"
													>
														{emoji}
													</Button>
												))}
											</Flex>
										</Box>
										<Box position="relative" h="300px" overflow="hidden">
											<EmojiPicker
												onEmojiClick={handleEmojiClickWithSend}
												theme={useColorModeValue("light", "dark")}
												width="100%"
												height={300}
												searchDisabled={false}
												skinTonesDisabled={false}
												previewConfig={{
													showPreview: true,
													defaultEmoji: "1f60a",
													defaultCaption: "Choose your emoji!"
												}}
												style={{
													border: 'none',
													backgroundColor: 'transparent'
												}}
												autoFocusSearch={true}
												emojiStyle="apple"
												lazyLoadEmojis={true}
												suggestedEmojisMode="recent"
											/>
											</Box>
										</>
									)}
								</Box>
							)}
						</Box>

						{/* Text input */}
						<Textarea
							ref={textareaRef}
							value={messageText}
							onChange={(e) => setMessageText(e.target.value)}
							onKeyDown={handleKeyDown}
							placeholder="Type a message..."
							className="telegram-textarea"
							minH="40px"
							maxH="120px"
							color={textColor}
							_placeholder={{ color: placeholderColor }}
							fontSize="15px"
							lineHeight="1.4"
							py={2}
							px={1}
							style={{ height: `${textareaHeight}px` }}
						/>
					</HStack>
				</Box>

				{/* Send/Voice button */}
				{messageText.trim() || imgUrl || selectedFile ? (
					<IconButton
						icon={isSending ? <Spinner size="sm" /> : <IoSendSharp />}
						size="lg"
						borderRadius="full"
						onClick={handleSendMessage}
						isDisabled={isSending}
						className="telegram-send-button"
						aria-label="Send message"
						h="44px"
						w="44px"
					/>
				) : (
					<IconButton
						icon={<FaMicrophone />}
						size="lg"
						borderRadius="full"
						onClick={handleVoiceRecording}
						className={`telegram-voice-button ${isRecording ? 'recording' : ''}`}
						aria-label={isRecording ? "Stop recording" : "Record voice message"}
						h="44px"
						w="44px"
					/>
				)}
			</Flex>

			{/* Hidden file input */}
			<input
				ref={fileInputRef}
				type="file"
				style={{ display: 'none' }}
				onChange={handleFileChange}
				accept="*/*"
			/>
		</Box>
	);
});

MessageInput.displayName = 'MessageInput';

export default MessageInput;
